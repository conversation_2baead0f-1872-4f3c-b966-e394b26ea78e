import { CancelRounded, CheckCircleRounded } from "@mui/icons-material";
import { <PERSON>, Link, Switch, Tooltip, Typography } from "@mui/material";
import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import { CompensationComponent, Formulae } from "src/services/api_definitions/payroll.service";
import { CompensationComponentContext } from "../CompensationComponents";

const ViewEarnings: React.FC<{
  earnings: CompensationComponent[];
}> = ({ earnings }) => {
  console.log({ earnings });
  const compensationComponentContext = React.useContext(CompensationComponentContext);

  const getFormattedFormula = (formula: Formulae) => {
    if (formula.calculation_type === "Percentage") {
      return `${formula.value}% of ${formula.display_name}`;
    }
    return formula.calculation_type === "System Defined" ? (
      <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary" }}>
        {formula.calculation_type}
      </Typography>
    ) : (
      formula.calculation_type
    );
  };

  const flattenAttributes = (attributes: Record<string, any>[]) => {
    const flat: Record<string, any> = {};
    attributes.forEach((attr) => {
      Object.entries(attr).forEach(([key, value]) => {
        flat[key] = value;
      });
    });
    return flat;
  };

  const transformedData = earnings.map((row) => ({
    ...row,
    ...flattenAttributes(row.attributes),
  }));

  const renderBooleanCells = (isChecked: boolean) => {
    return isChecked ? <CheckCircleRounded color="success" /> : <CancelRounded color="error" />;
  };

  return (
    <DataTable
      data={transformedData}
      state={{
        showSkeletons: compensationComponentContext.isLoading,
      }}
      positionActionsColumn="last"
      displayColumnDefOptions={{
        "mrt-row-actions": {
          size: 100,
          maxSize: 100,
          header: "",
          muiTableBodyCellProps: {
            align: "right",
          },
        },
      }}
      enableRowActions
      renderRowActions={({ row }) => (
        <Box display="flex" gap={1} alignItems="center" justifyContent="flex-end">
          <TableActions
            edit={{
              onClick: () => {
                compensationComponentContext.onEdit(row.original);
              },
              hide: true,
            }}
            remove={{
              onClick: () => {
                compensationComponentContext.deleteComponent(row.original.id);
              },
              hide: row.original.system_defined,
            }}
            view={{
              onClick: () => {
                compensationComponentContext.onView(row.original);
              },
              hide: true,
            }}
          />
          <Tooltip title={row.original.active ? "Deactivate" : "Activate"}>
            <Switch
              onChange={(_ev, checked) => compensationComponentContext.softDeleteComponent(row.original.id, checked)}
              checked={row.original.active}
              disabled={row.original.mandatory}
            />
          </Tooltip>
        </Box>
      )}
      columns={[
        {
          accessorKey: "name",
          header: "Component",
          size: 150,
          Cell: ({ cell, row }) => (
            <Link
              fontWeight="bold"
              underline="hover"
              sx={{ cursor: "pointer" }}
              onClick={() => compensationComponentContext.onEdit(row?.original)}
            >
              {cell.getValue()}
            </Link>
          ),
        },
        {
          accessorKey: "pay_type",
          header: "Calculation Type",
          size: 200,
          Cell: ({ row }) => (
            <EmployeeCellInfo
              name={row.original.formula.calculation_type}
              jobTitle={getFormattedFormula(row.original.formula) as string}
              hideAvatar
            />
          ),
        },
        {
          accessorKey: "consider_for_epf",
          header: "Consider for EPF",
          Cell: ({ cell }) => renderBooleanCells(cell.getValue<boolean>()),
          size: 50,
          muiTableBodyCellProps: {
            align: "center",
          },
          muiTableHeadCellProps: {
            align: "center",
          },
        },
        // {
        //   accessorKey: "taxability",
        //   header: "Taxability",
        //   Cell: ({ cell }) => renderBooleanCells(cell.getValue<boolean>()),
        //   size: 50,
        //   muiTableBodyCellProps: {
        //     align: "center",
        //   },
        // },
        // {
        //   accessorKey: "include_in_fbp",
        //   header: "Include in FBP",
        //   Cell: ({ cell }) => renderBooleanCells(cell.getValue<boolean>()),
        //   size: 50,
        //   muiTableBodyCellProps: {
        //     align: "center",
        //   },
        // },
        {
          accessorKey: "consider_for_esi",
          header: "Consider for ESI",
          Cell: ({ cell }) => renderBooleanCells(cell.getValue<boolean>()),
          size: 50,
          muiTableBodyCellProps: {
            align: "center",
          },
          muiTableHeadCellProps: {
            align: "center",
          },
        },
        // {
        //   accessorKey: "pro_rated",
        //   header: "Is Pro-rated",
        //   Cell: ({ cell }) => renderBooleanCells(cell.getValue<boolean>()),
        //   size: 50,
        //   muiTableBodyCellProps: {
        //     align: "center",
        //   },
        // },
      ]}
    />
  );
};

export default ViewEarnings;
