import { Box, Button, Grid2, Paper, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { CompensationAttributeKeys, CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";
import { CompensationComponentContext } from "../CompensationComponents";

const deductionsSchema = z.object({
  name: z.string().nonempty({
    message: "Deduction Name is required",
  }),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  selectedRow?: CompensationComponent | null;
};

const deductionConfigurations: {
  key: CompensationAttributeKeys;
  label: string;
  value: boolean | string | any;
  readonly?: boolean;
  options?: { label: string; value: string }[];
}[] = [
  {
    key: "taxability",
    label: "This is a taxable deduction",
    value: false,
  },
  {
    key: "ad_hoc",
    label: "This is an ad-hoc component",
    value: false,
    readonly: false,
  },
  {
    key: "pro_rated",
    label: "Pro-rated",
    value: false,
    readonly: false,
  },
  {
    key: "applied_frequency",
    label: "Applied Frequency",
    value: {
      checked: true,
      radio_group_value: "OneTime",
    },
    readonly: false,
    options: [
      { label: "One time deduction", value: "ONE_TIME" },
      { label: "Recurring deduction for subsequent Payrolls", value: "RECURRING" },
    ],
  },
];

const getComponentAttributes = (configurations: any) => {
  return configurations.reduce((acc, eachConfig) => {
    if (eachConfig.key === "applied_frequency") {
      acc[eachConfig.key] = eachConfig.value.radio_group_value || null;
      return acc;
    }
    acc[eachConfig.key] = eachConfig.value;
    return acc;
  }, {});
};

const AddEditDeductions: React.FC<Props> = ({ isOpen, onClose, title, selectedRow }) => {
  const isEditMode = useMemo(() => !!selectedRow, [selectedRow]);
  const { refetch } = React.useContext(CompensationComponentContext);

  const getValue = (
    key: CompensationAttributeKeys,
    defaultValue: boolean | string,
    selectedRow: CompensationComponent | null,
  ) => {
    if (isEditMode) {
      const selectedRowValue = selectedRow?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, key))?.[key];
      if (key === "applied_frequency") {
        return {
          checked: !!selectedRowValue,
          radio_group_value: selectedRowValue,
        };
      }

      if (key === "taxability") {
        return !!selectedRowValue;
      }
      return selectedRowValue || defaultValue;
    }
    return defaultValue;
  };

  const createEarningMutation = useMutation({
    mutationKey: ["create-earning"],
    mutationFn: async (payload: Partial<CompensationComponent>) => payrollService.createCompensationComponent(payload),
    onSuccess: () => {
      refetch();
      onClose();
    },
  });

  const updateEarningMutation = useMutation({
    mutationKey: ["update-earning"],
    mutationFn: async (payload: Partial<CompensationComponent>) => payrollService.updateCompensationComponent(payload),
    onSuccess: () => {
      refetch();
      onClose();
    },
  });

  const form = useAppForm({
    defaultValues: {
      ...selectedRow,
      id: selectedRow?.id || "",
      name: selectedRow?.name || "",
      calculationType: selectedRow?.formula?.calculation_type || "Flat",
      amount: selectedRow?.formula?.value || 0,
      payType: selectedRow?.pay_type || "Fixed",
      configurations: deductionConfigurations.map((eachConfig) => ({
        ...eachConfig,
        value: getValue(eachConfig.key, eachConfig.value, selectedRow),
        readonly:
          selectedRow?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, eachConfig.key))?.read_only ||
          selectedRow?.system_defined,
      })),
    },
    validators: {
      onSubmit: deductionsSchema, // Temporarily disabled for simplicity
    },
    onSubmit: ({ value }) => {
      const request: Partial<CompensationComponent> = {
        id: value.id,
        name: value.name,
        component_type: "Deduction",
        pay_type: value.payType,
        formula: {
          value: value.amount,
          code: value?.formula?.code,
          calculation_type: value.calculationType,
        },
        mandatory: true,
        pro_rated: false,
        taxable: false,
        ...getComponentAttributes(value.configurations),
      };

      if (isEditMode) {
        updateEarningMutation.mutate(request);
        return;
      }
      createEarningMutation.mutate(request);
    },
  });

  const inputProps = deductionConfigurations.map((eachConfig, index) => {
    return {
      fieldProps: {
        name: `configurations.${index}.value`,
        mode: "array",
      },
      formProps: {
        type: eachConfig.key === "applied_frequency" ? "radio-group-with-checkbox" : "checkbox",
        label: eachConfig.label,
        required: false,
        layout: eachConfig.key === "applied_frequency" ? "vertical" : "horizontal",
        options: eachConfig.options || [],
        disabled: form.getFieldValue(`configurations.${index}.readonly`),
      },
      containerProps: {
        size: 6,
      },
    };
  });

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      title={title}
      actions={
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button variant="outlined" onClick={() => onClose()}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isPristine || isSubmitting}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="name">
            {(field: any) => <field.EffiTextField label="Deduction Name" required placeholder="Enter deduction name" />}
          </form.AppField>
        </Grid2>
        <Grid2 size={12}>
          <Typography lineHeight={2} variant="h6" fontWeight={600} fontSize={18}>
            Configurations
          </Typography>
          <Box component={Paper} elevation={2} p={2} overflow="auto">
            <EffiDynamicForm form={form} inputFields={inputProps} />
          </Box>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditDeductions;
