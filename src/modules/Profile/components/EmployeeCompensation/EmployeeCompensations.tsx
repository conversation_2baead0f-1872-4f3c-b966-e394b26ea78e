import { ExpandMore, Visibility, VisibilityOff } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary, Button } from "@mui/material";
import { format } from "date-fns";
import React, { Key } from "react";
import { useMasterData } from "src/customHooks/useMasterData";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoData from "src/modules/Dashboard/component/QuickViews/components/NoDataScreens/NoData";
import { Country } from "src/services/api_definitions/location.service";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import EmployeeCompensation from "./EmployeeCompensation";

interface Props {
  compensations?: Pick<
    PayrollTemplateV2,
    "components" | "effective_date" | "template_name" | "effective_from" | "effective_to" | "ctc"
  >[];
  residentCountry: string;
}

const getCompensationTitles = (payroll: PayrollTemplateV2, isCurrent = false) => {
  if (isCurrent) {
    return `Current Compensation Details`;
  }
  if (!payroll?.effective_to) {
    return `Previous Compensation Details`;
  }
  return `Previous Compensation Details`;
};

const EmployeeCompensations: React.FC<Props> = ({ compensations = [], residentCountry }) => {
  const [isAmountHidden, setIsAmmountHidden] = React.useState<Record<number, boolean>>(
    compensations?.reduce(
      (acc, _curr, index: number) => {
        acc[index] = true;
        return acc;
      },
      {} as Record<number, boolean>,
    ),
  );
  const { data: countries } = useMasterData<Country>("Country");
  const formatLocale = countries?.find((country: Country) => country.name === residentCountry)?.code;

  if (!compensations || compensations?.length === 0) {
    <NoData title="No compensations found" />;
  }

  return (
    <>
      {[...compensations]?.map((payroll, index: number) => (
        <Accordion
          defaultExpanded={index === 0}
          key={`${payroll.template_name}-${index}`}
          slotProps={{ transition: { unmountOnExit: true } }}
        >
          <AccordionSummary expandIcon={<ExpandMore />} aria-controls="panel1-content" id="panel1-header">
            <ContentHeader
              title={getCompensationTitles(payroll as PayrollTemplateV2, index === 0)}
              actions={
                <Button
                  variant="text"
                  onClick={(ev) => {
                    ev.stopPropagation();
                    ev.preventDefault();
                    setIsAmmountHidden((prev) => {
                      return {
                        ...prev,
                        [index]: !prev[index],
                      };
                    });
                  }}
                  startIcon={isAmountHidden[index] ? <Visibility /> : <VisibilityOff />}
                >
                  {isAmountHidden[index] ? "Show" : "Hide"}
                </Button>
              }
            />
          </AccordionSummary>
          <AccordionDetails>
            <EmployeeCompensation
              compensation={payroll?.components || []}
              locale={formatLocale}
              currency={payroll?.components?.[0]?.compensation_component?.currency}
              ctc={payroll?.ctc}
              key={isAmountHidden[index] as unknown as Key}
              effectiveDate={payroll?.effective_from}
              effectiveTo={payroll?.effective_to}
              defaultAmountHidden={isAmountHidden[index]}
            />
          </AccordionDetails>
        </Accordion>
      ))}
    </>
  );
};

export default EmployeeCompensations;
